"use client";

import { useState, useEffect } from "react";
import <PERSON> from "next/link";
import Interview<PERSON>oom<PERSON> from "./InterviewRoomUI";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent } from "@/components/ui/card";

// Mock participant data for preview
const mockParticipants = [
  {
    identity: "John Doe-1234567890",
    isSpeaking: false,
  },
  {
    identity: "agent-interviewer",
    isSpeaking: true,
  },
];

export default function InterviewRoomPreview() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [speakingParticipant, setSpeakingParticipant] = useState(0);

  // Update time every second for a more realistic feel
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Simulate speaking animation by switching who's speaking
  useEffect(() => {
    const speakingTimer = setInterval(() => {
      setSpeakingParticipant((prev) => (prev + 1) % mockParticipants.length);
    }, 3000);

    return () => clearInterval(speakingTimer);
  }, []);

  const participants = mockParticipants.map((participant, index) => ({
    ...participant,
    isSpeaking: index === speakingParticipant,
  }));

  return (
    <div className="min-h-[calc(100vh-80px)] bg-background py-6">
      <div className="max-w-4xl mx-auto p-6">
        {/* Preview Banner */}
        <Alert className="mb-6 border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20">
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
            />
          </svg>
          <AlertDescription className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-1">
                🎭 Preview Mode
              </h3>
              <p className="text-blue-700 dark:text-blue-300 text-sm">
                This is a preview of what the interview room will look like. No
                actual connection is made.
              </p>
            </div>
            <Button asChild className="bg-green-600 hover:bg-green-700 ml-4">
              <Link href="/call">Start Real Interview</Link>
            </Button>
          </AlertDescription>
        </Alert>

        {/* Main Interview Room UI */}
        <InterviewRoomUI
          participants={participants}
          isPreview={true}
          previewTime={currentTime.toLocaleTimeString()}
          disconnectButton={
            <Button
              disabled
              variant="destructive"
              title="This is preview mode - button is disabled"
            >
              End Interview (Preview)
            </Button>
          }
        />

        {/* Additional Preview Information */}
        <Card className="mt-6">
          <CardContent className="pt-6">
            <h3 className="font-semibold text-foreground mb-2">
              What happens in a real interview:
            </h3>
            <ul className="text-sm text-foreground/70 space-y-1">
              <li>
                • You&apos;ll connect to a LiveKit room with audio enabled
              </li>
              <li>
                • The AI interviewer will join automatically and start the
                conversation
              </li>
              <li>
                • Your microphone will be active for real-time communication
              </li>
              <li>
                • The interview will be guided by your custom prompt from the
                Prompt Builder
              </li>
              <li>
                • You can end the interview at any time using the disconnect
                button
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

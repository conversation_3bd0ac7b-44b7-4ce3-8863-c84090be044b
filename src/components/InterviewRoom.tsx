"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>udi<PERSON><PERSON><PERSON><PERSON>,
  useParticipants,
  DisconnectButton,
} from "@livekit/components-react";
import InterviewRoom<PERSON> from "./InterviewRoomUI";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface RoomData {
  token: string;
  roomName: string;
  serverUrl: string;
  participantName: string;
}

interface InterviewRoomProps {
  roomData: RoomData;
  onDisconnect: () => void;
}

function RoomContent() {
  const participants = useParticipants();

  const disconnectButton = (
    <DisconnectButton
      className={cn(buttonVariants({ variant: "destructive" }))}
    >
      End Interview
    </DisconnectButton>
  );

  const additionalContent = (
    <>
      {/* This handles all audio rendering automatically */}
      <RoomAudioRenderer />
    </>
  );

  return (
    <div className="max-w-4xl mx-auto p-6">
      <InterviewRoomUI
        participants={participants}
        disconnectButton={disconnectButton}
        additionalContent={additionalContent}
      />
    </div>
  );
}

export default function InterviewRoom({
  roomData,
  onDisconnect,
}: InterviewRoomProps) {
  return (
    <div className="min-h-[calc(100vh-80px)] bg-background py-6">
      <LiveKitRoom
        token={roomData.token}
        serverUrl={roomData.serverUrl}
        connect={true}
        audio={true}
        video={false}
        onDisconnected={onDisconnect}
        onError={(error) => {
          console.error("LiveKit error:", error);
        }}
        style={{ height: "100%" }}
      >
        <RoomContent />
      </LiveKitRoom>
    </div>
  );
}

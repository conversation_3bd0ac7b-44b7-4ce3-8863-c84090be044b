import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function Header() {
  return (
    <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <Link
            href="/"
            className="text-xl font-bold text-foreground hover:text-blue-600 transition-colors"
          >
            Interview Next Service
          </Link>

          <nav className="flex items-center space-x-6">
            <Link
              href="/questionnaire-prompt-builder"
              className="text-foreground/70 hover:text-foreground transition-colors"
            >
              Prompt Builder
            </Link>
            <Link
              href="/preview"
              className="text-foreground/70 hover:text-foreground transition-colors"
            >
              Preview Room
            </Link>
            <Button asChild className="bg-green-600 hover:bg-green-700">
              <Link href="/call">Start Call</Link>
            </Button>
          </nav>
        </div>
      </div>
    </header>
  );
}

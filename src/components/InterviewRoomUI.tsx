"use client";

import { ReactNode } from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

interface Participant {
  identity: string;
  isSpeaking: boolean;
}

interface InterviewRoomUIProps {
  participants: Participant[];
  isPreview?: boolean;
  previewTime?: string;
  disconnectButton: ReactNode;
  additionalContent?: ReactNode;
}

interface ParticipantItemProps {
  participant: Participant;
  isPreview?: boolean;
}

function ParticipantItem({
  participant,
  isPreview = false,
}: ParticipantItemProps) {
  const isAgent = participant.identity.includes("agent");

  return (
    <Card
      className={`${isPreview ? "transition-all duration-300" : ""} ${
        isPreview && participant.isSpeaking ? "ring-2 ring-green-500/50" : ""
      }`}
    >
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          <div
            className={`w-3 h-3 rounded-full ${
              isAgent ? "bg-blue-500" : "bg-green-500"
            } ${
              isPreview
                ? `transition-colors duration-300 ${
                    participant.isSpeaking ? "animate-pulse" : ""
                  }`
                : ""
            }`}
          ></div>
          <span className="font-medium text-foreground">
            {isAgent ? "AI Interviewer" : participant.identity}
          </span>
          <span className="text-sm text-foreground/60">
            {participant.isSpeaking ? "🎤 Speaking" : "🔇 Silent"}
          </span>
          {isPreview && participant.isSpeaking && (
            <span className="text-xs text-green-600 dark:text-green-400 animate-pulse">
              ● LIVE
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default function InterviewRoomUI({
  participants,
  isPreview = false,
  previewTime,
  disconnectButton,
  additionalContent,
}: InterviewRoomUIProps) {
  return (
    <Card>
      <CardHeader className="text-center">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          Interview in Progress
        </h2>
        <p className="text-foreground/70">
          {participants.length > 1
            ? `Connected with AI Interviewer (${participants.length} participants)`
            : "Waiting for AI Interviewer to join..."}
        </p>
        {isPreview && previewTime && (
          <p className="text-sm text-foreground/50 mt-1">
            Preview started at {previewTime}
          </p>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {participants.map((participant, index) => (
            <ParticipantItem
              key={participant.identity || index}
              participant={participant}
              isPreview={isPreview}
            />
          ))}
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-foreground/60 mb-4">
            Make sure your microphone is enabled and speak clearly.
          </p>

          <div className={isPreview ? "space-y-3" : "mt-4"}>
            {disconnectButton}
            {isPreview && (
              <div className="text-xs text-foreground/50">
                In preview mode, controls are disabled
              </div>
            )}
          </div>
        </div>

        {additionalContent}
      </CardContent>
    </Card>
  );
}

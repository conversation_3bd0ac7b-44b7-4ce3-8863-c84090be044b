# Questionnaire Prompt Builder

## Overview

The Questionnaire Prompt Builder is a user-friendly interface that allows interviewers to create and customize AI interviewer prompts. This prompt will guide the AI agent during LiveKit interview sessions.

## Features

### ✨ Core Functionality

- **Create & Edit Prompts**: Professional textarea with real-time character count
- **Auto-Save**: Prompts are automatically persisted using the API
- **Reset to Default**: Quick reset to a professional default prompt
- **Real-time Character Count**: Track prompt length with color-coded feedback
- **Keyboard Shortcuts**: Save with Ctrl/Cmd + S
- **Smart Validation**: Contextual tooltips show validation requirements

### 🎨 User Experience

- **Modern UI Components**: Built with shadcn/ui for professional design
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Dark Mode Support**: Automatically adapts to system preferences
- **Loading States**: Clear feedback during save operations with disabled states
- **Enhanced Error Handling**: Alert components with retry functionality
- **Accessible Design**: Semantic HTML with proper label associations and keyboard navigation
- **Interactive Tooltips**: Hover guidance for form validation

### 📝 Prompt Writing Assistance

- **Example Prompts**: Built-in placeholder with professional examples
- **Writing Tips**: Helpful guidelines for creating effective interview prompts
- **Best Practices**: Suggestions for tone, structure, and content

## API Endpoints

### GET `/api/questionnaire-prompt-builder`

Returns the current prompt:

```json
{
  "prompt": "Your current interview prompt text..."
}
```

### POST `/api/questionnaire-prompt-builder`

Saves a new prompt:

```json
{
  "prompt": "Your new interview prompt text..."
}
```

Returns:

```json
{
  "success": true
}
```

## Usage

1. **Navigate to the Prompt Builder**: Visit `/questionnaire-prompt-builder`
2. **Edit Your Prompt**: Use the large textarea to craft your interview prompt
3. **Save Your Changes**: Click "Save Prompt" or use Ctrl/Cmd + S
4. **Start Interview**: Navigate to `/call` to begin the interview with your custom prompt

## Technical Implementation

- **Frontend**: Next.js 15 with React 19, TypeScript, and Tailwind CSS v4
- **UI Components**: shadcn/ui component library for consistent design
- **Backend**: Next.js API routes with in-memory storage
- **Styling**: Tailwind CSS v4 with shadcn/ui design tokens
- **State Management**: React hooks for local state
- **Form Handling**: Custom validation with real-time feedback
- **Accessibility**: Semantic HTML with label associations and keyboard navigation
- **Error Handling**: Alert components with retry functionality and user feedback

## File Structure

```
src/
├── app/
│   ├── questionnaire-prompt-builder/
│   │   └── page.tsx                 # Main prompt builder UI
│   ├── api/
│   │   └── questionnaire-prompt-builder/
│   │       └── route.ts             # API endpoints
│   └── call/
│       └── page.tsx                 # Interview call page
├── components/
│   ├── QuestionnairePromptBuilder.tsx # Main form component
│   ├── Header.tsx                   # Navigation header
│   ├── JoinForm.tsx                 # Interview join form
│   ├── InterviewRoom.tsx            # LiveKit room component
│   ├── InterviewRoomUI.tsx          # Room UI layout
│   ├── InterviewRoomPreview.tsx     # Preview mode
│   └── ui/                          # shadcn/ui components
│       ├── button.tsx               # Button component
│       ├── input.tsx                # Input component
│       ├── textarea.tsx             # Textarea component
│       ├── label.tsx                # Label component
│       ├── card.tsx                 # Card component
│       ├── alert.tsx                # Alert component
│       └── tooltip.tsx              # Tooltip component
├── lib/
│   └── utils.ts                     # Utility functions (cn helper)
└── globals.css                      # Global styles with shadcn/ui variables
```

## UI Components

The application uses shadcn/ui components for a consistent, accessible design:

### Form Components

- **Button**: Multiple variants (default, secondary, destructive, outline)
- **Input**: Styled text inputs with focus states
- **Textarea**: Multi-line text input with validation
- **Label**: Semantic form labels with proper associations

### Layout Components

- **Card**: Container components with consistent spacing
- **Alert**: Error and information displays with icons

### Interactive Components

- **Tooltip**: Contextual help that works even on disabled elements
- **TooltipProvider**: Manages tooltip behavior across the app

### Design Features

- **Consistent Spacing**: Standardized padding and margins
- **Focus Management**: Proper keyboard navigation
- **Color Tokens**: Semantic color system with dark mode support
- **Accessibility**: Semantic HTML structure with proper label associations

## Future Enhancements

- **Prompt Templates**: Pre-built templates for different interview types
- **Prompt History**: Version control for prompt changes
- **Collaborative Editing**: Multiple users editing prompts
- **Prompt Testing**: Preview mode to test prompts before interviews
- **Export/Import**: Save prompts to files or share between teams
